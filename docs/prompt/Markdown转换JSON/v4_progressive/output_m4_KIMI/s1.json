以下是根据提供的markdown内容进行分析后的完整数据结构：

```json
{
"type": "MONTHLY_REPORT",
"title": "慧芝湖花园3室2厅2卫价值评测报告",
"subtitle": "",
"content_segments": [
{
"segment_id": "seg_001",
"original_content": "# 慧芝湖花园3室2厅2卫价值评测报告",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "DOCUMENT",
"type_confidence": 0.9,
"style_confidence": 0.9,
"style_reasoning": "文档主标题，使用#标记",
"alternatives": []
},
"processing_notes": "文档主标题"
},
{
"segment_id": "seg_002",
"original_content": "## 报告基本信息\n- **数据来源**：上海市房地产交易平台\n- **评测时间**：2025年7月\n- **平均价格概览**：**97,600元/㎡**",
"content_type": "list",
"recommended_widget": {
"primary_type": "LIST",
"primary_style": "BULLET",
"type_confidence": 0.9,
"style_confidence": 0.9,
"style_reasoning": "使用符号列表标记，包含基本信息要点",
"alternatives": []
},
"processing_notes": "报告基本信息列表"
},
{
"segment_id": "seg_003",
"original_content": "## 评测房源基本信息",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "SECTION",
"type_confidence": 0.9,
"style_confidence": 0.9,
"style_reasoning": "章节标题，使用##标记",
"alternatives": []
},
"processing_notes": "章节标题"
},
{
"segment_id": "seg_004",
"original_content": "| 项目 | 详情 |\n|------|------|\n| 城市 | 上海市 |\n| 小区名称 | 慧芝湖花园 |\n| 户型 | 3室2厅2卫 |\n| 建筑面积 | 110㎡ |\n| 朝向 | 朝南 |\n| 预估单价 | 97,600元/㎡ |\n| 板块位置 | 凉城（挂牌板块：大宁板块） |",
"content_type": "table",
"recommended_widget": {
"primary_type": "TABLE",
"primary_style": "NORMAL",
"type_confidence": 0.9,
"style_confidence": 0.9,
"style_reasoning": "表格格式，使用markdown表格语法",
"alternatives": []
},
"processing_notes": "评测房源基本信息表格"
},
{
"segment_id": "seg_005",
"original_content": "**注**：本估值不包含装修价值",
"content_type": "paragraph",
"recommended_widget": {
"primary_type": "TEXT",
"primary_style": "PLAIN",
"type_confidence": 0.9,
"style_confidence": 0.9,
"style_reasoning": "普通文本内容，无特殊关键词或结构",
"alternatives": []
},
"processing_notes": "普通文本说明"
},
{
"segment_id": "seg_006",
"original_content": "## 小区基本信息分析",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "SECTION",
"type_confidence": 0.9,
"style_confidence": 0.9,
"style_reasoning": "章节标题，使用##标记",
"alternatives": []
},
"processing_notes": "章节标题"
},
{
"segment_id": "seg_007",
"original_content": "### 1. 小区户型分析",
"content_type": "title",
"recommended_widget": {
"primary_type": "TITLE",
"primary_style": "PARAGRAPH",
"type_confidence": 0.9,
"style_confidence": 0.9,
"style_reasoning": "段落标题，使用###标记",
"alternatives": []
},
"processing_notes": "段落标题"
},
{
"segment_id